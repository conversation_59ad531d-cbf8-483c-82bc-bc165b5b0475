import numpy as np
import pandas as pd
import joblib
import tensorflow as tf
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_absolute_error, r2_score
import warnings
warnings.filterwarnings('ignore')

class WeatherPredictor:
    def __init__(self, models_dir='models'):
        self.models_dir = models_dir
        self.models = {}
        self.model_scores = {}
        self.preprocessor = None
        self.best_model_name = None
        self.load_models()
    
    def load_models(self):
        """Load all trained models and preprocessor"""
        try:
            # Load preprocessor
            self.preprocessor = joblib.load(f'{self.models_dir}/preprocessor.pkl')
            
            # Load model scores
            self.model_scores = joblib.load(f'{self.models_dir}/model_scores.pkl')
            
            # Load models
            try:
                self.models['linear'] = joblib.load(f'{self.models_dir}/linear_model.pkl')
            except:
                print("Linear model not found")
            
            try:
                self.models['random_forest'] = joblib.load(f'{self.models_dir}/random_forest_model.pkl')
            except:
                print("Random Forest model not found")
            
            try:
                self.models['neural_network'] = tf.keras.models.load_model(f'{self.models_dir}/neural_network_model.h5')
            except:
                print("Neural Network model not found")
            
            try:
                self.models['lstm'] = tf.keras.models.load_model(f'{self.models_dir}/lstm_model.h5')
            except:
                print("LSTM model not found")
            
            # Find best model
            best_score = -float('inf')
            for model_name, scores in self.model_scores.items():
                if scores['val_r2'] > best_score:
                    best_score = scores['val_r2']
                    self.best_model_name = model_name
            
            print(f"Loaded {len(self.models)} models. Best: {self.best_model_name}")
            
        except Exception as e:
            print(f"Error loading models: {e}")
    
    def calculate_confidence(self, model_name, prediction, features):
        """Calculate confidence based on model performance and prediction uncertainty"""
        try:
            # Base confidence from model validation score
            base_confidence = self.model_scores.get(model_name, {}).get('val_r2', 0.5)
            base_confidence = max(0.3, min(0.95, base_confidence))
            
            # For Random Forest, we can get prediction uncertainty
            if model_name == 'random_forest' and hasattr(self.models[model_name], 'estimators_'):
                # Get predictions from all trees
                tree_predictions = []
                for estimator in self.models[model_name].estimators_:
                    tree_pred = estimator.predict(features)[0]
                    tree_predictions.append(tree_pred)
                
                # Calculate standard deviation as uncertainty measure
                pred_std = np.std(tree_predictions)
                uncertainty = min(pred_std / (abs(prediction) + 1), 0.5)  # Normalize
                confidence = base_confidence * (1 - uncertainty)
            else:
                # For other models, use simpler approach
                confidence = base_confidence
            
            return max(0.3, min(0.95, confidence))
            
        except Exception as e:
            return 0.7  # Default confidence
    
    def prepare_input_features(self, current_data):
        """Prepare input features from current weather data"""
        if isinstance(current_data, dict):
            df = pd.DataFrame([current_data])
        else:
            df = current_data.copy()
        
        # Add time if not present
        if 'Time' not in df.columns:
            df['Time'] = datetime.now()
        
        # Feature engineering (same as training)
        df = self.preprocessor.feature_engineering(df)
        X, _ = self.preprocessor.prepare_features(df)
        X_scaled = self.preprocessor.scaler.transform(X)
        
        return X_scaled
    
    def predict_rainfall(self, current_data, time_horizons=[6, 12, 24, 48, 72]):
        """Predict rainfall for multiple time horizons with proper confidence"""
        predictions = {}
        
        try:
            # Prepare input features
            X_input = self.prepare_input_features(current_data)
            
            # Use best model for predictions
            best_model = self.models[self.best_model_name]
            
            for hours in time_horizons:
                # Make prediction
                if self.best_model_name in ['lstm', 'neural_network']:
                    pred = best_model.predict(X_input, verbose=0)[0][0]
                else:
                    pred = best_model.predict(X_input)[0]
                
                # Add some uncertainty for longer horizons
                uncertainty_factor = 1 + (hours / 24) * 0.05
                pred_adjusted = max(0, pred * uncertainty_factor)
                
                # Calculate proper confidence
                confidence = self.calculate_confidence(self.best_model_name, pred_adjusted, X_input)
                
                # Reduce confidence for longer horizons
                time_penalty = max(0.3, 1 - (hours / 72) * 0.4)
                final_confidence = confidence * time_penalty
                
                predictions[f'{hours}h'] = {
                    'rainfall_mm': round(pred_adjusted, 2),
                    'confidence': round(final_confidence, 3),
                    'horizon': f'{hours} hours',
                    'model_uncertainty': round(1 - confidence, 3)
                }
            
            return predictions
            
        except Exception as e:
            print(f"Error in rainfall prediction: {e}")
            return {}
    
    def predict_daily_rainfall(self, current_data, days=[1, 2, 3]):
        """Predict daily rainfall with proper confidence"""
        daily_predictions = {}
        
        try:
            X_input = self.prepare_input_features(current_data)
            best_model = self.models[self.best_model_name]
            
            for day in days:
                # Base prediction
                if self.best_model_name in ['lstm', 'neural_network']:
                    base_pred = best_model.predict(X_input, verbose=0)[0][0]
                else:
                    base_pred = best_model.predict(X_input)[0]
                
                # Adjust for day ahead
                day_factor = 1 + (day - 1) * 0.1
                daily_pred = max(0, base_pred * day_factor)
                
                # Calculate confidence
                confidence = self.calculate_confidence(self.best_model_name, daily_pred, X_input)
                time_penalty = max(0.2, 1 - (day - 1) * 0.3)
                final_confidence = confidence * time_penalty
                
                daily_predictions[f'day_{day}'] = {
                    'date': (datetime.now() + timedelta(days=day)).strftime('%Y-%m-%d'),
                    'rainfall_mm': round(daily_pred, 2),
                    'confidence': round(final_confidence, 3),
                    'category': self._categorize_rainfall(daily_pred),
                    'model_uncertainty': round(1 - confidence, 3)
                }
            
            return daily_predictions
            
        except Exception as e:
            print(f"Error in daily prediction: {e}")
            return {}
    
    def _categorize_rainfall(self, rainfall_mm):
        """Categorize rainfall amount"""
        if rainfall_mm < 0.1:
            return "No Rain"
        elif rainfall_mm < 2.5:
            return "Light Rain"
        elif rainfall_mm < 10:
            return "Moderate Rain"
        elif rainfall_mm < 35:
            return "Heavy Rain"
        else:
            return "Very Heavy Rain"
    
    def get_prediction_summary(self, current_data):
        """Get comprehensive prediction summary"""
        hourly_pred = self.predict_rainfall(current_data)
        daily_pred = self.predict_daily_rainfall(current_data)
        
        summary = {
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'model_used': self.best_model_name,
            'hourly_predictions': hourly_pred,
            'daily_predictions': daily_pred,
            'location': current_data.get('Observed Location', 'Mumbai') if isinstance(current_data, dict) else 'Mumbai'
        }
        
        return summary
    
    def get_actual_vs_predicted(self, selected_date, weather_data):
        """Get actual vs predicted comparison for a specific date"""
        try:
            # Load test results if available
            import pandas as pd
            test_df = pd.read_csv('test_results_2024.csv')
            test_df['Time'] = pd.to_datetime(test_df['Time'])
            
            # Find closest date
            selected_datetime = pd.to_datetime(selected_date)
            test_df['date_diff'] = abs((test_df['Time'] - selected_datetime).dt.total_seconds())
            closest_idx = test_df['date_diff'].idxmin()
            closest_data = test_df.iloc[closest_idx]
            
            # Get actual rainfall
            actual_rainfall = closest_data.get('actual_rainfall', None)
            
            # Get predictions from all models
            X_input = self.prepare_input_features(weather_data)
            model_predictions = {}
            
            for model_name, model in self.models.items():
                try:
                    if model_name in ['lstm', 'neural_network']:
                        pred = model.predict(X_input, verbose=0)[0][0]
                    else:
                        pred = model.predict(X_input)[0]
                    
                    # Also get stored prediction if available
                    stored_pred = closest_data.get(f'{model_name}_prediction', None)
                    
                    model_predictions[model_name] = {
                        'live_prediction': round(max(0, pred), 2),
                        'stored_prediction': round(stored_pred, 2) if stored_pred is not None else None,
                        'model_score': self.model_scores.get(model_name, {}).get('val_r2', 0)
                    }
                except Exception as e:
                    print(f"Error predicting with {model_name}: {e}")
            
            return {
                'actual_rainfall': round(actual_rainfall, 2) if actual_rainfall is not None else None,
                'date': closest_data['Time'].strftime('%Y-%m-%d %H:%M'),
                'model_predictions': model_predictions,
                'best_model': self.best_model_name
            }
            
        except Exception as e:
            print(f"Error getting actual vs predicted: {e}")
            return None
