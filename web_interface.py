#!/usr/bin/env python3
"""
Flask web interface for weather prediction
"""

from flask import Flask, render_template, request, jsonify
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from src.prediction_engine import WeatherPredictor
import json

app = Flask(__name__)

# Initialize predictor
predictor = WeatherPredictor('models')

def load_historical_data():
    """Load historical data for the last 4 months of 2024"""
    try:
        df = pd.read_csv('test_results_2024.csv')
        df['Time'] = pd.to_datetime(df['Time'])
        return df
    except:
        # Fallback to original data
        from src.data_preprocessing import WeatherDataPreprocessor
        preprocessor = WeatherDataPreprocessor()
        df = preprocessor.load_and_clean_data('cleaned_data.csv')
        df = preprocessor.feature_engineering(df)
        
        # Filter last 4 months of 2024
        df_2024 = df[df['Time'].dt.year == 2024]
        last_date = df_2024['Time'].max()
        test_start = last_date - pd.DateOffset(months=4)
        return df_2024[df_2024['Time'] >= test_start].copy()

historical_data = load_historical_data()

@app.route('/')
def index():
    """Main page"""
    # Get date range for last 4 months of 2024
    min_date = historical_data['Time'].min().strftime('%Y-%m-%d')
    max_date = historical_data['Time'].max().strftime('%Y-%m-%d')
    
    return render_template('index.html', min_date=min_date, max_date=max_date)

@app.route('/predict', methods=['POST'])
def predict():
    """Make prediction for selected date with actual vs predicted comparison"""
    try:
        data = request.json
        selected_date = datetime.strptime(data['date'], '%Y-%m-%d')
        prediction_type = data['prediction_type']
        
        # Find closest historical data point
        historical_data['date_diff'] = abs((historical_data['Time'] - selected_date).dt.total_seconds())
        closest_idx = historical_data['date_diff'].idxmin()
        closest_data = historical_data.iloc[closest_idx]
        
        # Prepare weather data for prediction
        weather_data = {
            'Time': selected_date,
            'Temperature': closest_data.get('Temperature', 28.0),
            'Relative Humidity': closest_data.get('Relative Humidity', 75.0),
            'Pressure': closest_data.get('Pressure', 1013.25),
            'Weather Phrase': closest_data.get('Weather Phrase', 'Partly Cloudy'),
            'Dew Point': closest_data.get('Dew Point', 24.0),
            'Heat Index': closest_data.get('Heat Index', 32.0),
            'Visibility': closest_data.get('Visibility', 10.0),
            'Wind Direction': closest_data.get('Wind Direction', 270.0),
            'Wind Cardinal': closest_data.get('Wind Cardinal', 'W'),
            'UV Index': closest_data.get('UV Index', 6),
            'UV Description': closest_data.get('UV Description', 'High'),
            'Feels Like': closest_data.get('Feels Like', 32.0)
        }
        
        # Get predictions based on type
        if prediction_type == 'hourly':
            predictions = predictor.predict_rainfall(weather_data)
        elif prediction_type == 'daily':
            predictions = predictor.predict_daily_rainfall(weather_data)
        else:  # both
            hourly_pred = predictor.predict_rainfall(weather_data)
            daily_pred = predictor.predict_daily_rainfall(weather_data)
            predictions = {
                'hourly': hourly_pred,
                'daily': daily_pred
            }
        
        # Get actual vs predicted comparison
        comparison = predictor.get_actual_vs_predicted(selected_date, weather_data)
        if comparison:
            predictions['comparison'] = comparison
        
        # Add weather conditions
        predictions['weather_conditions'] = {
            'temperature': weather_data['Temperature'],
            'humidity': weather_data['Relative Humidity'],
            'pressure': weather_data['Pressure'],
            'weather_phrase': weather_data['Weather Phrase'],
            'date': selected_date.strftime('%Y-%m-%d')
        }
        
        return jsonify({
            'success': True,
            'predictions': predictions,
            'model_used': predictor.best_model_name
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

@app.route('/historical/<date>')
def get_historical(date):
    """Get historical data for a specific date"""
    try:
        selected_date = datetime.strptime(date, '%Y-%m-%d')
        
        # Find data for the selected date
        day_data = historical_data[
            historical_data['Time'].dt.date == selected_date.date()
        ]
        
        if len(day_data) > 0:
            data_point = day_data.iloc[0]
            return jsonify({
                'success': True,
                'data': {
                    'temperature': data_point.get('Temperature', 'N/A'),
                    'humidity': data_point.get('Relative Humidity', 'N/A'),
                    'pressure': data_point.get('Pressure', 'N/A'),
                    'weather_phrase': data_point.get('Weather Phrase', 'N/A'),
                    'actual_rainfall': data_point.get('actual_rainfall', 'N/A')
                }
            })
        else:
            return jsonify({
                'success': False,
                'error': 'No data found for selected date'
            })
            
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        })

if __name__ == '__main__':
    app.run(debug=True, port=5000)
