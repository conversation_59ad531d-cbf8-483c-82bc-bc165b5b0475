#!/usr/bin/env python3
"""
Weather Prediction Inference Script
"""

import pandas as pd
import numpy as np
from datetime import datetime
from src.prediction_engine import WeatherPredictor
import json

def get_sample_current_weather():
    """Get sample current weather data"""
    return {
        'Observed Location': 'Mumbai',
        'Time': datetime.now(),
        'Temperature': 28.5,
        'Weather Phrase': 'Partly Cloudy',
        'Dew Point': 24.0,
        'Heat Index': 32.0,
        'Relative Humidity': 75.0,
        'Pressure': 1013.25,
        'Visibility': 10.0,
        'Wind Direction': 270.0,
        'Wind Cardinal': 'W',
        'UV Index': 6,
        'UV Description': 'High',
        'Feels Like': 32.0
    }

def main():
    print("=== Weather Rainfall Prediction ===\n")
    
    # Initialize predictor
    predictor = WeatherPredictor('models')
    
    if not predictor.models:
        print("❌ No trained models found. Please run 'python train_models.py' first.")
        return
    
    # Get current weather data (in real scenario, this would come from API)
    print("Using sample current weather data...")
    current_weather = get_sample_current_weather()
    
    print("Current Weather Conditions:")
    print(f"Location: {current_weather['Observed Location']}")
    print(f"Temperature: {current_weather['Temperature']}°C")
    print(f"Humidity: {current_weather['Relative Humidity']}%")
    print(f"Pressure: {current_weather['Pressure']} hPa")
    print(f"Weather: {current_weather['Weather Phrase']}")
    
    # Get predictions
    print("\n" + "="*60)
    print("RAINFALL PREDICTIONS")
    print("="*60)
    
    # Hourly predictions
    print("\n📊 HOURLY PREDICTIONS:")
    print("-" * 40)
    hourly_pred = predictor.predict_rainfall(current_weather)
    
    for horizon, pred in hourly_pred.items():
        confidence_bar = "█" * int(pred['confidence'] * 10)
        print(f"{pred['horizon']:12} | {pred['rainfall_mm']:6.2f} mm | Confidence: {confidence_bar} {pred['confidence']:.1%}")
    
    # Daily predictions
    print("\n📅 DAILY PREDICTIONS:")
    print("-" * 50)
    daily_pred = predictor.predict_daily_rainfall(current_weather)
    
    for day_key, pred in daily_pred.items():
        confidence_bar = "█" * int(pred['confidence'] * 10)
        print(f"{pred['date']} | {pred['rainfall_mm']:6.2f} mm | {pred['category']:15} | {confidence_bar} {pred['confidence']:.1%}")
    
    # Full summary
    print("\n📋 PREDICTION SUMMARY:")
    print("-" * 50)
    summary = predictor.get_prediction_summary(current_weather)
    
    print(f"Model Used: {summary['model_used']}")
    print(f"Timestamp: {summary['timestamp']}")
    
    # Save predictions to file
    with open('latest_predictions.json', 'w') as f:
        json.dump(summary, f, indent=2, default=str)
    
    print(f"\n💾 Predictions saved to 'latest_predictions.json'")
    
    # Interactive mode
    print("\n" + "="*60)
    print("INTERACTIVE MODE")
    print("="*60)
    print("Enter custom weather values (press Enter to use default):")
    
    try:
        temp = input(f"Temperature ({current_weather['Temperature']}°C): ").strip()
        if temp:
            current_weather['Temperature'] = float(temp)
        
        humidity = input(f"Humidity ({current_weather['Relative Humidity']}%): ").strip()
        if humidity:
            current_weather['Relative Humidity'] = float(humidity)
        
        pressure = input(f"Pressure ({current_weather['Pressure']} hPa): ").strip()
        if pressure:
            current_weather['Pressure'] = float(pressure)
        
        # Get new predictions
        print("\n🔄 Updated Predictions:")
        new_summary = predictor.get_prediction_summary(current_weather)
        
        print("\nNext 24 hours:")
        for horizon in ['6h', '12h', '24h']:
            if horizon in new_summary['hourly_predictions']:
                pred = new_summary['hourly_predictions'][horizon]
                print(f"  {horizon:4}: {pred['rainfall_mm']:5.2f} mm")
        
        print("\nNext 3 days:")
        for day in ['day_1', 'day_2', 'day_3']:
            if day in new_summary['daily_predictions']:
                pred = new_summary['daily_predictions'][day]
                print(f"  {pred['date']}: {pred['rainfall_mm']:5.2f} mm ({pred['category']})")
    
    except KeyboardInterrupt:
        print("\n\nGoodbye! 👋")
    except Exception as e:
        print(f"\nError in interactive mode: {e}")

if __name__ == "__main__":
    main()