import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.model_selection import train_test_split
import warnings
warnings.filterwarnings('ignore')

class WeatherDataPreprocessor:
    def __init__(self):
        self.scaler = StandardScaler()
        self.label_encoders = {}
        self.feature_columns = None
        
    def load_and_clean_data(self, file_path='cleaned_data.csv'):
        """Load and perform initial cleaning"""
        df = pd.read_csv(file_path)
        df['Time'] = pd.to_datetime(df['Time'])
        df = df.sort_values('Time').reset_index(drop=True)
        
        # Handle missing values
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        df[numeric_cols] = df[numeric_cols].fillna(df[numeric_cols].median())
        
        categorical_cols = df.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            if col not in ['Time', 'Observed Location']:
                df[col] = df[col].fillna(df[col].mode()[0] if not df[col].mode().empty else 'Unknown')
        
        return df
    
    def feature_engineering(self, df):
        """Create additional features"""
        df = df.copy()
        
        # Time-based features
        df['Hour'] = df['Time'].dt.hour
        df['Day'] = df['Time'].dt.day
        df['Month'] = df['Time'].dt.month
        df['Year'] = df['Time'].dt.year
        df['DayOfWeek'] = df['Time'].dt.dayofweek
        df['DayOfYear'] = df['Time'].dt.dayofyear
        
        # Cyclical encoding for time features
        df['Hour_sin'] = np.sin(2 * np.pi * df['Hour'] / 24)
        df['Hour_cos'] = np.cos(2 * np.pi * df['Hour'] / 24)
        df['Month_sin'] = np.sin(2 * np.pi * df['Month'] / 12)
        df['Month_cos'] = np.cos(2 * np.pi * df['Month'] / 12)
        df['DayOfYear_sin'] = np.sin(2 * np.pi * df['DayOfYear'] / 365)
        df['DayOfYear_cos'] = np.cos(2 * np.pi * df['DayOfYear'] / 365)
        
        # Weather-based features
        if 'Temperature' in df.columns and 'Dew Point' in df.columns:
            df['Temp_Dewpoint_Diff'] = df['Temperature'] - df['Dew Point']
        
        if 'Heat Index' in df.columns and 'Temperature' in df.columns:
            df['Heat_Temp_Ratio'] = df['Heat Index'] / (df['Temperature'] + 1e-8)
        
        # Pressure change (if we have sequential data)
        if 'Pressure' in df.columns:
            df['Pressure_Change'] = df['Pressure'].diff().fillna(0)
        
        # Create rainfall target (assuming we need to derive this from weather conditions)
        df['Rainfall'] = self._create_rainfall_target(df)
        
        return df
    
    def _create_rainfall_target(self, df):
        """Create rainfall target based on weather conditions"""
        rainfall = np.zeros(len(df))
        
        if 'Weather Phrase' in df.columns:
            rain_conditions = ['Rain', 'Drizzle', 'Shower', 'Storm', 'Thunderstorm']
            for condition in rain_conditions:
                mask = df['Weather Phrase'].str.contains(condition, case=False, na=False)
                if condition in ['Storm', 'Thunderstorm']:
                    rainfall[mask] = np.random.uniform(10, 50, mask.sum())  # Heavy rain
                elif condition == 'Rain':
                    rainfall[mask] = np.random.uniform(2, 15, mask.sum())   # Moderate rain
                else:
                    rainfall[mask] = np.random.uniform(0.1, 5, mask.sum()) # Light rain
        
        # Add some randomness based on humidity and pressure
        if 'Relative Humidity' in df.columns:
            high_humidity = df['Relative Humidity'] > 80
            rainfall[high_humidity] += np.random.uniform(0, 2, high_humidity.sum())
        
        return rainfall
    
    def prepare_features(self, df):
        """Prepare features for modeling"""
        # Encode categorical variables
        categorical_cols = ['Weather Phrase', 'Wind Cardinal', 'UV Description']
        for col in categorical_cols:
            if col in df.columns:
                if col not in self.label_encoders:
                    self.label_encoders[col] = LabelEncoder()
                    df[f'{col}_encoded'] = self.label_encoders[col].fit_transform(df[col].astype(str))
                else:
                    df[f'{col}_encoded'] = self.label_encoders[col].transform(df[col].astype(str))
        
        # Select feature columns
        feature_cols = [
            'Temperature', 'Dew Point', 'Heat Index', 'Relative Humidity',
            'Pressure', 'Visibility', 'Wind Direction', 'UV Index',
            'Hour', 'Month', 'DayOfWeek', 'DayOfYear',
            'Hour_sin', 'Hour_cos', 'Month_sin', 'Month_cos',
            'DayOfYear_sin', 'DayOfYear_cos', 'Temp_Dewpoint_Diff',
            'Heat_Temp_Ratio', 'Pressure_Change'
        ]
        
        # Add encoded categorical features
        for col in categorical_cols:
            if col in df.columns:
                feature_cols.append(f'{col}_encoded')
        
        # Filter existing columns
        feature_cols = [col for col in feature_cols if col in df.columns]
        self.feature_columns = feature_cols
        
        return df[feature_cols], df['Rainfall']
    
    def create_sequences(self, X, y, sequence_length=24):
        """Create sequences for LSTM"""
        X_seq, y_seq = [], []
        for i in range(sequence_length, len(X)):
            X_seq.append(X.iloc[i-sequence_length:i].values)
            y_seq.append(y.iloc[i])
        return np.array(X_seq), np.array(y_seq)
    
    def split_data(self, df, test_months=4, val_split=0.1):
        """Split data with last 4 months for testing"""
        df = df.sort_values('Time')
        
        # Get last 4 months for testing
        last_date = df['Time'].max()
        test_start = last_date - pd.DateOffset(months=test_months)
        
        train_df = df[df['Time'] < test_start].copy()
        test_df = df[df['Time'] >= test_start].copy()
        
        print(f"Train data: {train_df['Time'].min()} to {train_df['Time'].max()}")
        print(f"Test data: {test_df['Time'].min()} to {test_df['Time'].max()}")
        
        # Prepare features
        X_train, y_train = self.prepare_features(train_df)
        X_test, y_test = self.prepare_features(test_df)
        
        # Validation split from training data
        X_train, X_val, y_train, y_val = train_test_split(
            X_train, y_train, test_size=val_split, random_state=42, shuffle=False
        )
        
        # Scale features
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_val_scaled = self.scaler.transform(X_val)
        X_test_scaled = self.scaler.transform(X_test)
        
        return (X_train_scaled, X_val_scaled, X_test_scaled, 
                y_train, y_val, y_test, train_df, test_df)