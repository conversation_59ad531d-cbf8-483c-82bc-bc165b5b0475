#!/usr/bin/env python3
"""
Test models on last 4 months of 2024 data
"""

import pandas as pd
import numpy as np
import joblib
import tensorflow as tf
from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
from src.data_preprocessing import WeatherDataPreprocessor
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_test_data():
    """Load and prepare test data from last 4 months of 2024"""
    preprocessor = joblib.load('models/preprocessor.pkl')
    
    # Load data
    df = preprocessor.load_and_clean_data('cleaned_data.csv')
    df = preprocessor.feature_engineering(df)
    
    # Filter last 4 months of 2024
    df_2024 = df[df['Time'].dt.year == 2024]
    last_date = df_2024['Time'].max()
    test_start = last_date - pd.DateOffset(months=4)
    
    test_df = df_2024[df_2024['Time'] >= test_start].copy()
    print(f"Test period: {test_df['Time'].min()} to {test_df['Time'].max()}")
    print(f"Test samples: {len(test_df)}")
    
    # Prepare features
    X_test, y_test = preprocessor.prepare_features(test_df)
    X_test_scaled = preprocessor.scaler.transform(X_test)
    
    return X_test_scaled, y_test, test_df

def test_all_models():
    """Test all trained models on 2024 data"""
    print("=== Testing Models on Last 4 Months of 2024 ===\n")
    
    # Load test data
    X_test, y_test, test_df = load_test_data()
    
    # Load model scores
    model_scores = joblib.load('models/model_scores.pkl')
    
    # Test each model
    results = {}
    
    # Linear model
    try:
        linear_model = joblib.load('models/linear_model.pkl')
        linear_pred = linear_model.predict(X_test)
        
        results['linear'] = {
            'predictions': linear_pred,
            'r2': r2_score(y_test, linear_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, linear_pred)),
            'mae': mean_absolute_error(y_test, linear_pred)
        }
        print(f"Linear Regression - R²: {results['linear']['r2']:.4f}, RMSE: {results['linear']['rmse']:.4f}")
    except Exception as e:
        print(f"Linear model test failed: {e}")
    
    # Random Forest
    try:
        rf_model = joblib.load('models/random_forest_model.pkl')
        rf_pred = rf_model.predict(X_test)
        
        results['random_forest'] = {
            'predictions': rf_pred,
            'r2': r2_score(y_test, rf_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, rf_pred)),
            'mae': mean_absolute_error(y_test, rf_pred)
        }
        print(f"Random Forest - R²: {results['random_forest']['r2']:.4f}, RMSE: {results['random_forest']['rmse']:.4f}")
    except Exception as e:
        print(f"Random Forest test failed: {e}")
    
    # Neural Network
    try:
        nn_model = tf.keras.models.load_model('models/neural_network_model.h5')
        nn_pred = nn_model.predict(X_test, verbose=0).flatten()
        
        results['neural_network'] = {
            'predictions': nn_pred,
            'r2': r2_score(y_test, nn_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, nn_pred)),
            'mae': mean_absolute_error(y_test, nn_pred)
        }
        print(f"Neural Network - R²: {results['neural_network']['r2']:.4f}, RMSE: {results['neural_network']['rmse']:.4f}")
    except Exception as e:
        print(f"Neural Network test failed: {e}")
    
    # LSTM
    try:
        lstm_model = tf.keras.models.load_model('models/lstm_model.h5')
        lstm_pred = lstm_model.predict(X_test, verbose=0).flatten()
        
        results['lstm'] = {
            'predictions': lstm_pred,
            'r2': r2_score(y_test, lstm_pred),
            'rmse': np.sqrt(mean_squared_error(y_test, lstm_pred)),
            'mae': mean_absolute_error(y_test, lstm_pred)
        }
        print(f"LSTM - R²: {results['lstm']['r2']:.4f}, RMSE: {results['lstm']['rmse']:.4f}")
    except Exception as e:
        print(f"LSTM test failed: {e}")
    
    # Save results
    test_results_df = test_df.copy()
    test_results_df['actual_rainfall'] = y_test
    
    for model_name, result in results.items():
        test_results_df[f'{model_name}_prediction'] = result['predictions']
    
    test_results_df.to_csv('test_results_2024.csv', index=False)
    
    # Summary
    print("\n=== Model Performance Summary ===")
    print("-" * 50)
    for model_name, result in results.items():
        print(f"{model_name:15} | R²: {result['r2']:6.4f} | RMSE: {result['rmse']:6.4f} | MAE: {result['mae']:6.4f}")
    
    return results, test_results_df

if __name__ == "__main__":
    results, test_df = test_all_models()