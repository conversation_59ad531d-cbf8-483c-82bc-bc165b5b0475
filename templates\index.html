<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Weather Rainfall Prediction</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #2d3436, #636e72);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .main-content {
            padding: 40px;
        }
        
        .input-section {
            background: #f8f9fa;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2d3436;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        
        .input-group input:focus, .input-group select:focus {
            outline: none;
            border-color: #74b9ff;
        }
        
        .button-group {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
            min-width: 150px;
        }
        
        .btn-primary {
            background: #74b9ff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0984e3;
            transform: translateY(-2px);
        }
        
        .btn-secondary {
            background: #00b894;
            color: white;
        }
        
        .btn-secondary:hover {
            background: #00a085;
            transform: translateY(-2px);
        }
        
        .btn-info {
            background: #fdcb6e;
            color: #2d3436;
        }
        
        .btn-info:hover {
            background: #e17055;
            color: white;
            transform: translateY(-2px);
        }
        
        .results-section {
            margin-top: 30px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            display: none;
        }
        
        .weather-info {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border-left: 4px solid #74b9ff;
        }
        
        .prediction-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .prediction-card h3 {
            color: #2d3436;
            margin-bottom: 15px;
            border-bottom: 2px solid #74b9ff;
            padding-bottom: 10px;
        }
        
        .prediction-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #eee;
        }
        
        .prediction-item:last-child {
            border-bottom: none;
        }
        
        .confidence-bar {
            width: 100px;
            height: 8px;
            background: #eee;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .confidence-fill {
            height: 100%;
            background: linear-gradient(90deg, #00b894, #74b9ff);
            transition: width 0.3s;
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            color: #636e72;
        }
        
        .error {
            background: #ff7675;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        .success {
            background: #00b894;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }
        
        @media (max-width: 768px) {
            .button-group {
                flex-direction: column;
            }
            
            .btn {
                min-width: auto;
            }
            
            .prediction-item {
                flex-direction: column;
                align-items: flex-start;
                gap: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🌧️ Weather Rainfall Prediction</h1>
            <p>AI-powered rainfall forecasting for Mumbai using multiple ML models</p>
        </div>
        
        <div class="main-content">
            <div class="input-section">
                <h2>Select Date and Prediction Type</h2>
                <p>Choose any date from the last 4 months of 2024 to get rainfall predictions</p>
                
                <div class="input-group">
                    <label for="prediction-date">Select Date:</label>
                    <input type="date" id="prediction-date" 
                           min="{{ min_date }}" 
                           max="{{ max_date }}" 
                           value="{{ max_date }}">
                </div>
                
                <div class="button-group">
                    <button class="btn btn-primary" onclick="makePrediction('hourly')">
                        📊 Hourly Predictions
                    </button>
                    <button class="btn btn-secondary" onclick="makePrediction('daily')">
                        📅 Daily Predictions
                    </button>
                    <button class="btn btn-info" onclick="makePrediction('both')">
                        🔍 Complete Analysis
                    </button>
                </div>
                
                <div style="margin-top: 20px;">
                    <button class="btn" style="background: #636e72; color: white;" onclick="getHistoricalData()">
                        📈 View Historical Data
                    </button>
                </div>
            </div>
            
            <div id="results" class="results-section">
                <!-- Results will be populated here -->
            </div>
        </div>
    </div>

    <script>
        async function makePrediction(type) {
            const date = document.getElementById('prediction-date').value;
            if (!date) {
                alert('Please select a date');
                return;
            }
            
            const resultsDiv = document.getElementById('results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML = '<div class="loading">🔄 Making predictions...</div>';
            
            try {
                const response = await fetch('/predict', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        date: date,
                        prediction_type: type
                    })
                });
                
                const data = await response.json();
                
                if (data.success) {
                    displayResults(data.predictions, data.model_used, type);
                } else {
                    resultsDiv.innerHTML = `<div class="error">❌ Error: ${data.error}</div>`;
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="error">❌ Network error: ${error.message}</div>`;
            }
        }
        
        function displayResults(predictions, modelUsed, type) {
            const resultsDiv = document.getElementById('results');
            let html = '';
            
            // Weather conditions
            if (predictions.weather_conditions) {
                const weather = predictions.weather_conditions;
                html += `
                    <div class="weather-info">
                        <h3>🌤️ Weather Conditions for ${weather.date}</h3>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-top: 15px;">
                            <div><strong>Temperature:</strong> ${weather.temperature}°C</div>
                            <div><strong>Humidity:</strong> ${weather.humidity}%</div>
                            <div><strong>Pressure:</strong> ${weather.pressure} hPa</div>
                            <div><strong>Conditions:</strong> ${weather.weather_phrase}</div>
                        </div>
                        <div style="margin-top: 15px; padding: 10px; background: #e3f2fd; border-radius: 5px;">
                            <strong>🤖 Best Model:</strong> ${modelUsed}
                        </div>
                    </div>
                `;
            }
            
            // Actual vs Predicted Comparison
            if (predictions.comparison) {
                const comp = predictions.comparison;
                html += `
                    <div class="prediction-card">
                        <h3>📊 Actual vs Predicted Comparison</h3>
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                                <span><strong>📅 Date:</strong> ${comp.date}</span>
                                <span style="font-size: 1.3em; font-weight: bold; color: #e17055;">
                                    <strong>🌧️ Actual:</strong> ${comp.actual_rainfall !== null ? comp.actual_rainfall + ' mm' : 'N/A'}
                                </span>
                            </div>
                        </div>
                        
                        <h4 style="margin-bottom: 15px; color: #2d3436;">Model Predictions vs Actual:</h4>
                `;
                
                Object.entries(comp.model_predictions).forEach(([modelName, predData]) => {
                    const accuracy = comp.actual_rainfall !== null && predData.stored_prediction !== null ? 
                        Math.max(0, 100 - Math.abs(comp.actual_rainfall - predData.stored_prediction) / (comp.actual_rainfall + 0.1) * 100) : null;
                    
                    const isCurrentBest = modelName === comp.best_model;
                    
                    html += `
                        <div class="prediction-item" style="background: ${isCurrentBest ? '#e3f2fd' : 'white'}; padding: 15px; border-radius: 8px; margin-bottom: 10px;">
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; align-items: center;">
                                <div>
                                    <strong style="color: ${isCurrentBest ? '#0984e3' : '#2d3436'};">
                                        ${modelName.replace('_', ' ').toUpperCase()}
                                        ${isCurrentBest ? ' ⭐' : ''}
                                    </strong>
                                    <br>
                                    <small>R² Score: ${(predData.model_score * 100).toFixed(1)}%</small>
                                </div>
                                <div style="text-align: center;">
                                    <strong>Live Prediction</strong><br>
                                    <span style="color: #0984e3; font-size: 1.1em;">${predData.live_prediction} mm</span>
                                </div>
                                <div style="text-align: center;">
                                    <strong>Stored Prediction</strong><br>
                                    <span style="color: #00b894; font-size: 1.1em;">
                                        ${predData.stored_prediction !== null ? predData.stored_prediction + ' mm' : 'N/A'}
                                    </span>
                                </div>
                                <div style="text-align: center;">
                                    <strong>Accuracy</strong><br>
                                    <span style="color: ${accuracy !== null ? (accuracy > 80 ? '#00b894' : accuracy > 60 ? '#fdcb6e' : '#ff7675') : '#636e72'}; font-size: 1.1em;">
                                        ${accuracy !== null ? accuracy.toFixed(1) + '%' : 'N/A'}
                                    </span>
                                </div>
                            </div>
                        </div>
                    `;
                });
                
                html += '</div>';
            }
            
            // Hourly predictions
            if (type === 'hourly' || type === 'both') {
                const hourlyPred = type === 'both' ? predictions.hourly : predictions;
                if (hourlyPred) {
                    html += `
                        <div class="prediction-card">
                            <h3>📊 Hourly Rainfall Predictions</h3>
                            <div style="margin-bottom: 10px; padding: 10px; background: #fff3cd; border-radius: 5px;">
                                <small><strong>Confidence Explanation:</strong> Based on model validation score (${(predictor?.model_scores?.[modelUsed]?.val_r2 * 100 || 70).toFixed(1)}%) and prediction uncertainty. Higher confidence = more reliable prediction.</small>
                            </div>
                    `;
                    
                    Object.entries(hourlyPred).forEach(([key, pred]) => {
                        const confidencePercent = Math.round(pred.confidence * 100);
                        const uncertaintyPercent = Math.round((pred.model_uncertainty || 0) * 100);
                        
                        html += `
                            <div class="prediction-item">
                                <div>
                                    <strong>${pred.horizon}</strong><br>
                                    <span style="color: #636e72;">${pred.rainfall_mm} mm</span><br>
                                    <small style="color: #e17055;">Uncertainty: ${uncertaintyPercent}%</small>
                                </div>
                                <div style="text-align: right;">
                                    <div>Confidence: ${confidencePercent}%</div>
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: ${confidencePercent}%"></div>
                                    </div>
                                    <small style="color: #636e72;">Model: ${modelUsed}</small>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                }
            }
            
            // Daily predictions
            if (type === 'daily' || type === 'both') {
                const dailyPred = type === 'both' ? predictions.daily : predictions;
                if (dailyPred) {
                    html += `
                        <div class="prediction-card">
                            <h3>📅 Daily Rainfall Predictions</h3>
                            <div style="margin-bottom: 10px; padding: 10px; background: #fff3cd; border-radius: 5px;">
                                <small><strong>Confidence Explanation:</strong> Decreases with time horizon. Day 1 = highest confidence, Day 3 = lowest confidence.</small>
                            </div>
                    `;
                    
                    Object.entries(dailyPred).forEach(([key, pred]) => {
                        const confidencePercent = Math.round(pred.confidence * 100);
                        const uncertaintyPercent = Math.round((pred.model_uncertainty || 0) * 100);
                        
                        html += `
                            <div class="prediction-item">
                                <div>
                                    <strong>${pred.date}</strong><br>
                                    <span style="color: #636e72;">${pred.rainfall_mm} mm - ${pred.category}</span><br>
                                    <small style="color: #e17055;">Uncertainty: ${uncertaintyPercent}%</small>
                                </div>
                                <div style="text-align: right;">
                                    <div>Confidence: ${confidencePercent}%</div>
                                    <div class="confidence-bar">
                                        <div class="confidence-fill" style="width: ${confidencePercent}%"></div>
                                    </div>
                                    <small style="color: #636e72;">Model: ${modelUsed}</small>
                                </div>
                            </div>
                        `;
                    });
                    
                    html += '</div>';
                }
            }
            
            resultsDiv.innerHTML = html;
        }
        
        async function getHistoricalData() {
            const date = document.getElementById('prediction-date').value;
            if (!date) {
                alert('Please select a date');
                return;
            }
            
            try {
                const response = await fetch(`/historical/${date}`);
                const data = await response.json();
                
                if (data.success) {
                    const historical = data.data;
                    const resultsDiv = document.getElementById('results');
                    resultsDiv.style.display = 'block';
                    resultsDiv.innerHTML = `
                        <div class="prediction-card">
                            <h3>📈 Historical Weather Data for ${date}</h3>
                            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                                <div><strong>Temperature:</strong> ${historical.temperature}°C</div>
                                <div><strong>Humidity:</strong> ${historical.humidity}%</div>
                                <div><strong>Pressure:</strong> ${historical.pressure} hPa</div>
                                <div><strong>Weather:</strong> ${historical.weather_phrase}</div>
                                <div><strong>Actual Rainfall:</strong> ${historical.actual_rainfall} mm</div>
                            </div>
                        </div>
                    `;
                } else {
                    alert(`Error: ${data.error}`);
                }
            } catch (error) {
                alert(`Network error: ${error.message}`);
            }
        }
        
        // Set default date to latest available
        document.addEventListener('DOMContentLoaded', function() {
            const dateInput = document.getElementById('prediction-date');
            dateInput.value = '{{ max_date }}';
        });
    </script>
</body>
</html>
