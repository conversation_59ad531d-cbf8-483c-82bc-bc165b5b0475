import numpy as np
import pandas as pd
from sklearn.linear_model import LinearRegression, Ridge
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LST<PERSON>, Dense, Dropout
from tensorflow.keras.optimizers import Adam
import joblib
import os

class WeatherModels:
    def __init__(self):
        self.models = {}
        self.model_scores = {}
        
    def train_linear_model(self, X_train, y_train, X_val, y_val):
        """Train Linear Regression model"""
        model = Ridge(alpha=1.0)
        model.fit(X_train, y_train)
        
        # Predictions
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)
        
        # Scores
        train_score = r2_score(y_train, train_pred)
        val_score = r2_score(y_val, val_pred)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
        val_mae = mean_absolute_error(y_val, val_pred)
        
        self.models['linear'] = model
        self.model_scores['linear'] = {
            'train_r2': train_score,
            'val_r2': val_score,
            'val_rmse': val_rmse,
            'val_mae': val_mae
        }
        
        print(f"Linear Model - Train R²: {train_score:.4f}, Val R²: {val_score:.4f}, RMSE: {val_rmse:.4f}")
        return model
    
    def train_random_forest(self, X_train, y_train, X_val, y_val):
        """Train Random Forest model"""
        model = RandomForestRegressor(
            n_estimators=100,
            max_depth=15,
            min_samples_split=5,
            min_samples_leaf=2,
            random_state=42,
            n_jobs=-1
        )
        model.fit(X_train, y_train)
        
        # Predictions
        train_pred = model.predict(X_train)
        val_pred = model.predict(X_val)
        
        # Scores
        train_score = r2_score(y_train, train_pred)
        val_score = r2_score(y_val, val_pred)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
        val_mae = mean_absolute_error(y_val, val_pred)
        
        self.models['random_forest'] = model
        self.model_scores['random_forest'] = {
            'train_r2': train_score,
            'val_r2': val_score,
            'val_rmse': val_rmse,
            'val_mae': val_mae
        }
        
        print(f"Random Forest - Train R²: {train_score:.4f}, Val R²: {val_score:.4f}, RMSE: {val_rmse:.4f}")
        return model
    
    def train_lstm(self, X_train, y_train, X_val, y_val, sequence_length=24):
        """Train LSTM model"""
        # Reshape data for LSTM
        X_train_seq = X_train.reshape((X_train.shape[0], sequence_length, X_train.shape[1] // sequence_length))
        X_val_seq = X_val.reshape((X_val.shape[0], sequence_length, X_val.shape[1] // sequence_length))
        
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=(sequence_length, X_train_seq.shape[2])),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            Dense(25),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        
        # Train model
        history = model.fit(
            X_train_seq, y_train,
            validation_data=(X_val_seq, y_val),
            epochs=50,
            batch_size=32,
            verbose=0
        )
        
        # Predictions
        train_pred = model.predict(X_train_seq).flatten()
        val_pred = model.predict(X_val_seq).flatten()
        
        # Scores
        train_score = r2_score(y_train, train_pred)
        val_score = r2_score(y_val, val_pred)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
        val_mae = mean_absolute_error(y_val, val_pred)
        
        self.models['lstm'] = model
        self.model_scores['lstm'] = {
            'train_r2': train_score,
            'val_r2': val_score,
            'val_rmse': val_rmse,
            'val_mae': val_mae
        }
        
        print(f"LSTM - Train R²: {train_score:.4f}, Val R²: {val_score:.4f}, RMSE: {val_rmse:.4f}")
        return model
    
    def train_neural_network(self, X_train, y_train, X_val, y_val):
        """Train Neural Network model"""
        model = Sequential([
            Dense(128, activation='relu', input_shape=(X_train.shape[1],)),
            Dropout(0.3),
            Dense(64, activation='relu'),
            Dropout(0.3),
            Dense(32, activation='relu'),
            Dropout(0.2),
            Dense(16, activation='relu'),
            Dense(1)
        ])
        
        model.compile(optimizer=Adam(learning_rate=0.001), loss='mse', metrics=['mae'])
        
        # Train model
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=100,
            batch_size=32,
            verbose=0
        )
        
        # Predictions
        train_pred = model.predict(X_train).flatten()
        val_pred = model.predict(X_val).flatten()
        
        # Scores
        train_score = r2_score(y_train, train_pred)
        val_score = r2_score(y_val, val_pred)
        val_rmse = np.sqrt(mean_squared_error(y_val, val_pred))
        val_mae = mean_absolute_error(y_val, val_pred)
        
        self.models['neural_network'] = model
        self.model_scores['neural_network'] = {
            'train_r2': train_score,
            'val_r2': val_score,
            'val_rmse': val_rmse,
            'val_mae': val_mae
        }
        
        print(f"Neural Network - Train R²: {train_score:.4f}, Val R²: {val_score:.4f}, RMSE: {val_rmse:.4f}")
        return model
    
    def get_best_model(self):
        """Get the best performing model based on validation R²"""
        if not self.model_scores:
            return None, None
        
        best_model_name = max(self.model_scores.keys(), 
                            key=lambda x: self.model_scores[x]['val_r2'])
        best_model = self.models[best_model_name]
        
        print(f"\nBest Model: {best_model_name}")
        print(f"Validation R²: {self.model_scores[best_model_name]['val_r2']:.4f}")
        print(f"Validation RMSE: {self.model_scores[best_model_name]['val_rmse']:.4f}")
        
        return best_model_name, best_model
    
    def save_models(self, save_dir='models'):
        """Save all trained models"""
        os.makedirs(save_dir, exist_ok=True)
        
        for name, model in self.models.items():
            if name in ['lstm', 'neural_network']:
                model.save(f'{save_dir}/{name}_model.h5')
            else:
                joblib.dump(model, f'{save_dir}/{name}_model.pkl')
        
        # Save scores
        joblib.dump(self.model_scores, f'{save_dir}/model_scores.pkl')
        print(f"Models saved to {save_dir}/")