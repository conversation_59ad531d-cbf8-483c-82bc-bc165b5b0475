#!/usr/bin/env python3
"""
Weather Prediction Model Training Script
"""

import pandas as pd
import numpy as np
import joblib
import os
from src.data_preprocessing import WeatherDataPreprocessor
from src.models import WeatherModels
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== Weather Prediction Model Training ===\n")
    
    # Initialize preprocessor and models
    preprocessor = WeatherDataPreprocessor()
    models = WeatherModels()
    
    # Load and preprocess data
    print("1. Loading and preprocessing data...")
    df = preprocessor.load_and_clean_data('cleaned_data.csv')
    df = preprocessor.feature_engineering(df)
    
    print(f"Data shape: {df.shape}")
    print(f"Date range: {df['Time'].min()} to {df['Time'].max()}")
    
    # Split data (last 4 months for testing, 10% of training for validation)
    print("\n2. Splitting data...")
    (X_train, X_val, X_test, 
     y_train, y_val, y_test, 
     train_df, test_df) = preprocessor.split_data(df, test_months=4, val_split=0.1)
    
    print(f"Training set: {X_train.shape[0]} samples")
    print(f"Validation set: {X_val.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")
    
    # Train models
    print("\n3. Training models...")
    print("-" * 50)
    
    # Linear Regression
    print("Training Linear Regression...")
    models.train_linear_model(X_train, y_train, X_val, y_val)
    
    # Random Forest
    print("Training Random Forest...")
    models.train_random_forest(X_train, y_train, X_val, y_val)
    
    # Neural Network
    print("Training Neural Network...")
    models.train_neural_network(X_train, y_train, X_val, y_val)
    
    # LSTM (simplified - using same data structure)
    print("Training LSTM...")
    try:
        models.train_lstm(X_train, y_train, X_val, y_val)
    except Exception as e:
        print(f"LSTM training failed: {e}")
    
    # Get best model
    print("\n4. Model Comparison:")
    print("-" * 50)
    for model_name, scores in models.model_scores.items():
        print(f"{model_name:15} - Val R²: {scores['val_r2']:.4f}, RMSE: {scores['val_rmse']:.4f}")
    
    best_name, best_model = models.get_best_model()
    
    # Test on test set
    print("\n5. Testing best model on test set...")
    if best_name in ['lstm', 'neural_network']:
        test_pred = best_model.predict(X_test).flatten()
    else:
        test_pred = best_model.predict(X_test)
    
    from sklearn.metrics import r2_score, mean_squared_error, mean_absolute_error
    test_r2 = r2_score(y_test, test_pred)
    test_rmse = np.sqrt(mean_squared_error(y_test, test_pred))
    test_mae = mean_absolute_error(y_test, test_pred)
    
    print(f"Test R²: {test_r2:.4f}")
    print(f"Test RMSE: {test_rmse:.4f}")
    print(f"Test MAE: {test_mae:.4f}")
    
    # Save models and preprocessor
    print("\n6. Saving models...")
    os.makedirs('models', exist_ok=True)
    models.save_models('models')
    joblib.dump(preprocessor, 'models/preprocessor.pkl')
    
    # Save feature importance for tree-based models
    if 'random_forest' in models.models:
        rf_model = models.models['random_forest']
        feature_importance = pd.DataFrame({
            'feature': preprocessor.feature_columns,
            'importance': rf_model.feature_importances_
        }).sort_values('importance', ascending=False)
        
        print("\nTop 10 Important Features (Random Forest):")
        print(feature_importance.head(10))
        feature_importance.to_csv('models/feature_importance.csv', index=False)
    
    print(f"\n✅ Training completed! Best model: {best_name}")
    print("Models saved to 'models/' directory")

if __name__ == "__main__":
    main()