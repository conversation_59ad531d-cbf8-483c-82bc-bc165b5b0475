#!/usr/bin/env python3
"""
Run tests on 2024 data and start web interface
"""

import subprocess
import sys
import os

def run_tests():
    """Run model tests on 2024 data"""
    print("=== Running Model Tests on 2024 Data ===")
    try:
        subprocess.run([sys.executable, 'test_models.py'], check=True)
        print("✅ Tests completed successfully!")
    except subprocess.CalledProcessError as e:
        print(f"❌ Tests failed: {e}")
        return False
    return True

def start_web_interface():
    """Start the web interface"""
    print("\n=== Starting Web Interface ===")
    print("🌐 Web interface will be available at: http://localhost:5000")
    print("Press Ctrl+C to stop the server")
    
    try:
        subprocess.run([sys.executable, 'web_interface.py'], check=True)
    except KeyboardInterrupt:
        print("\n👋 Web server stopped")
    except subprocess.CalledProcessError as e:
        print(f"❌ Web server failed: {e}")

def main():
    print("Weather Prediction System - Test & Web Interface")
    print("=" * 50)
    
    # Check if models exist
    if not os.path.exists('models'):
        print("❌ No models found. Please run 'python train_models.py' first.")
        return
    
    # Run tests
    if run_tests():
        # Start web interface
        start_web_interface()
    else:
        print("❌ Cannot start web interface due to test failures")

if __name__ == "__main__":
    main()