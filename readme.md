# Weather Rainfall Prediction System

A comprehensive machine learning system for predicting rainfall in Mumbai using multiple models and time horizons.

## Features

- **Multiple ML Models**: Linear Regression, Random Forest, LSTM, Neural Networks
- **Multi-horizon Predictions**: 6hr, 12hr, 1-day, 2-day, 3-day forecasts
- **Feature Engineering**: Time-based, weather-based, and cyclical features
- **Model Comparison**: Automatic selection of best performing model
- **Interactive Predictions**: Command-line interface for real-time predictions

## Project Structure

```
├── src/
│   ├── __init__.py
│   ├── data_preprocessing.py    # Data cleaning and feature engineering
│   ├── models.py               # ML model implementations
│   └── prediction_engine.py    # Prediction and inference engine
├── models/                     # Saved trained models
├── train_models.py            # Training script
├── predict.py                 # Prediction script
├── requirements.txt           # Dependencies
└── README.md                  # This file
```

## Installation

1. Install dependencies:
```bash
pip install -r requirements.txt
```

## Usage

### 1. Train Models

```bash
python train_models.py
```

This will:
- Load and preprocess `cleaned_data.csv`
- Split data (last 4 months for testing, 10% for validation)
- Train multiple models (Linear, Random Forest, LSTM, Neural Network)
- Compare models and save the best one
- Save all models to `models/` directory

### 2. Make Predictions

```bash
python predict.py
```

This will:
- Load the best trained model
- Show sample predictions for different time horizons
- Provide interactive mode for custom weather inputs

## Model Performance

The system automatically selects the best model based on validation R² score. Typical performance:

- **Random Forest**: Usually best for this type of data
- **Neural Network**: Good for complex patterns
- **LSTM**: Best for sequential patterns (if enough data)
- **Linear**: Baseline model

## Prediction Horizons

- **6 hours**: High confidence, short-term forecast
- **12 hours**: Good confidence, half-day forecast  
- **1 day**: Medium confidence, next day forecast
- **2 days**: Lower confidence, day after tomorrow
- **3 days**: Lowest confidence, 3-day forecast

## Data Requirements

The system expects `cleaned_data.csv` with columns:
- `Time`: Timestamp
- `Temperature`: Air temperature (°C)
- `Relative Humidity`: Humidity percentage
- `Pressure`: Atmospheric pressure (hPa)
- `Weather Phrase`: Weather description
- `Wind Direction`: Wind direction (degrees)
- Other weather parameters...

## Example Output

```
📊 HOURLY PREDICTIONS:
6 hours      |   2.45 mm | Confidence: ████████ 85%
12 hours     |   3.12 mm | Confidence: ███████ 78%
24 hours     |   4.67 mm | Confidence: ██████ 65%

📅 DAILY PREDICTIONS:
2024-01-15 |   4.67 mm | Moderate Rain   | ██████ 65%
2024-01-16 |   5.23 mm | Moderate Rain   | █████ 52%
2024-01-17 |   3.89 mm | Light Rain      | ████ 42%
```

## Customization

- Modify `src/data_preprocessing.py` for different feature engineering
- Add new models in `src/models.py`
- Adjust prediction horizons in `src/prediction_engine.py`
- Change validation strategy in training script

## Notes

- The system creates synthetic rainfall targets from weather conditions
- For production use, replace with actual rainfall measurements
- Model performance depends on data quality and quantity
- Consider retraining models periodically with new data

