Rainfall: The target variable. You need high-frequency data, ideally hourly rainfall amounts (in mm).

Temperature: Air temperature, dew point. The difference between them is related to humidity.

Atmospheric Pressure: Sea-level pressure. Falling pressure often indicates approaching storms.

Humidity: Relative humidity (%). Higher humidity means more moisture is available to form rain.

Wind: Wind speed and wind direction. Wind direction is crucial in Mumbai, as monsoon winds from the Arabian Sea bring moisture.

Cloud Cover: Measured in Oktas (eighths of the sky covered).

Radar Data: This is the most important data for short-term prediction (next 1-6 hours). Weather radar imagery shows the location, intensity, and movement of existing rain clouds.

Satellite Imagery: Data from satellites like INSAT-3D provides information on cloud top temperature, water vapor, and wind patterns over a large area like the Arabian Sea, which is essential for tracking developing weather systems.

Numerical Weather Prediction (NWP) Model Data: This is computer-generated forecast data from global models like GFS (from NOAA) or ECMWF. These models use atmospheric physics to predict variables at different altitudes. Key NWP variables include:

Geopotential Height: The height of a pressure surface (e.g., 500 hPa). It helps identify large-scale high and low-pressure systems.

Relative Humidity at different levels (e.g., 700 hPa): Shows where moisture is in the atmosphere, not just at the surface.

Wind fields at different levels: Tracks the movement of weather systems.

CAPE (Convective Available Potential Energy): Measures the instability of the atmosphere. High CAPE values suggest a higher likelihood of thunderstorms.

Topography: Static data like the elevation and distance from the coast for each station.

You should include the same core weather data (rainfall, temperature, wind, pressure) from a network of weather stations in key surrounding areas:

North: Southern Gujarat (e.g., Surat, Valsad).

East: Areas just across the Western Ghats (e.g., Pune, Nashik).

South: The Konkan coast (e.g., Raigad, Ratnagiri).

Sea Surface Temperature (SST): Warmer sea temperatures lead to more evaporation and more moisture in the atmosphere, fueling stronger rainfall.

Satellite-Derived Wind and Water Vapor: Satellite data can show the movement of moisture and the strength of the monsoon winds over the sea before they make landfall.

Buoy Data: If available, data from weather buoys in the Arabian Sea provides direct measurements of pressure, wind, and temperature over the ocean.

Madden-Julian Oscillation (MJO) Index: The MJO is a large, eastward-moving pulse of clouds and rainfall near the equator that circles the globe every 30-60 days. When the active phase of the MJO is over the Indian Ocean, it significantly enhances monsoon rainfall. This is a key driver for "active" vs. "break" periods in the monsoon.

Monsoon Low-Pressure Systems: Track the formation and movement of low-pressure areas and depressions, especially those that form in the Bay of Bengal and travel westwards across India, as they often bring widespread, heavy rain.
