{"cells": [{"cell_type": "code", "execution_count": 1, "id": "4da678d0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Observed Location</th>\n", "      <th>Time</th>\n", "      <th>Day</th>\n", "      <th>Temperature</th>\n", "      <th>Weather Icon</th>\n", "      <th>Weather Phrase</th>\n", "      <th>Dew Point</th>\n", "      <th>Heat Index</th>\n", "      <th>Relative Humidity</th>\n", "      <th>Pressure</th>\n", "      <th>Visibility</th>\n", "      <th>Wind Chill</th>\n", "      <th>Wind Direction</th>\n", "      <th><PERSON></th>\n", "      <th>UV Index</th>\n", "      <th>UV Description</th>\n", "      <th>Feels Like</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Mumbai</td>\n", "      <td>2009-10-01 00:10:00</td>\n", "      <td>N</td>\n", "      <td>29.0</td>\n", "      <td>15</td>\n", "      <td>Partly Cloudy</td>\n", "      <td>27.0</td>\n", "      <td>37.0</td>\n", "      <td>89.0</td>\n", "      <td>1004.52</td>\n", "      <td>3.0</td>\n", "      <td>29.0</td>\n", "      <td>260.0</td>\n", "      <td>W</td>\n", "      <td>0</td>\n", "      <td>Low</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Mumbai</td>\n", "      <td>2009-10-01 00:40:00</td>\n", "      <td>N</td>\n", "      <td>29.0</td>\n", "      <td>15</td>\n", "      <td>Partly Cloudy</td>\n", "      <td>27.0</td>\n", "      <td>37.0</td>\n", "      <td>89.0</td>\n", "      <td>1004.52</td>\n", "      <td>3.0</td>\n", "      <td>29.0</td>\n", "      <td>240.0</td>\n", "      <td>WSW</td>\n", "      <td>0</td>\n", "      <td>Low</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Mumbai</td>\n", "      <td>2009-10-01 01:10:00</td>\n", "      <td>N</td>\n", "      <td>29.0</td>\n", "      <td>15</td>\n", "      <td>Partly Cloudy</td>\n", "      <td>27.0</td>\n", "      <td>37.0</td>\n", "      <td>89.0</td>\n", "      <td>1003.52</td>\n", "      <td>3.0</td>\n", "      <td>29.0</td>\n", "      <td>0.0</td>\n", "      <td>CALM</td>\n", "      <td>0</td>\n", "      <td>Low</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Mumbai</td>\n", "      <td>2009-10-01 01:40:00</td>\n", "      <td>N</td>\n", "      <td>29.0</td>\n", "      <td>15</td>\n", "      <td>Partly Cloudy</td>\n", "      <td>27.0</td>\n", "      <td>37.0</td>\n", "      <td>89.0</td>\n", "      <td>1003.52</td>\n", "      <td>3.0</td>\n", "      <td>29.0</td>\n", "      <td>220.0</td>\n", "      <td>SW</td>\n", "      <td>0</td>\n", "      <td>Low</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Mumbai</td>\n", "      <td>2009-10-01 02:10:00</td>\n", "      <td>N</td>\n", "      <td>29.0</td>\n", "      <td>15</td>\n", "      <td>Partly Cloudy</td>\n", "      <td>27.0</td>\n", "      <td>37.0</td>\n", "      <td>89.0</td>\n", "      <td>1003.52</td>\n", "      <td>3.0</td>\n", "      <td>29.0</td>\n", "      <td>240.0</td>\n", "      <td>WSW</td>\n", "      <td>0</td>\n", "      <td>Low</td>\n", "      <td>37.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["  Observed Location                 Time Day  Temperature  Weather Icon  \\\n", "0            Mumbai  2009-10-01 00:10:00   N         29.0            15   \n", "1            Mumbai  2009-10-01 00:40:00   N         29.0            15   \n", "2            Mumbai  2009-10-01 01:10:00   N         29.0            15   \n", "3            Mumbai  2009-10-01 01:40:00   N         29.0            15   \n", "4            Mumbai  2009-10-01 02:10:00   N         29.0            15   \n", "\n", "  Weather Phrase  Dew Point  Heat Index  Relative Humidity  Pressure  \\\n", "0  Partly Cloudy       27.0        37.0               89.0   1004.52   \n", "1  Partly Cloudy       27.0        37.0               89.0   1004.52   \n", "2  Partly Cloudy       27.0        37.0               89.0   1003.52   \n", "3  Partly Cloudy       27.0        37.0               89.0   1003.52   \n", "4  Partly Cloudy       27.0        37.0               89.0   1003.52   \n", "\n", "   Visibility  Wind Chill  Wind Direction Wind Cardinal  UV Index  \\\n", "0         3.0        29.0           260.0             W         0   \n", "1         3.0        29.0           240.0           WSW         0   \n", "2         3.0        29.0             0.0          CALM         0   \n", "3         3.0        29.0           220.0            SW         0   \n", "4         3.0        29.0           240.0           WSW         0   \n", "\n", "  UV Description  Feels Like  \n", "0            Low        37.0  \n", "1            Low        37.0  \n", "2            Low        37.0  \n", "3            Low        37.0  \n", "4            Low        37.0  "]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "data = pd.read_csv('cleaned_data.csv')\n", "data.head()"]}, {"cell_type": "code", "execution_count": null, "id": "2ce2cca6", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}